class ApplicationController < ActionController::Base
  include Miscellany::JsonUploads

  protect_from_forgery with: :exception
  prepend_before_action :forbid_access_if_lacking_session
  check_authorization

  rescue_from CanCan::AccessDenied, Miscellany::HttpErrorHandling::UnauthorizedError do |_e|
    respond_to do |format|
      format.html { render file: 'public/401.html', status: :unauthorized }
      format.json do
        render json: { message: 'You are not authorized to view this page' }, status: 401
      end
    end
  end

  rescue_from ActiveRecord::RecordInvalid do |e|
    errors_hash = render_validation_errors(e.record, e) if respond_to?(:render_validation_errors)
    next if performed?

    locals = { error: e, record: e.record }
    locals[:errors_hash] = errors_hash if errors_hash.is_a?(Hash)
    render template: 'api/v1/validation_errors', locals: locals, status: 422
  end

  def redirect_client_to(path)
    respond_to do |format|
      format.html { redirect_to path }
      format.json do
        render json: { redirect: path }
      end
    end
  end

  def current_account
    @current_account ||= Account.find_or_initialize_by(
      canvas_id: current_session.get_lti_cust_param('canvas_account_id')
    )
  end

  def current_user
    debugger
    canvas_user_id = current_session.get_lti_cust_param('custom_canvas_user_id')
    @current_user ||= User.find_or_initialize_by(canvas_id: canvas_user_id) do |u|
      u.sync_from_api
    end
  end

  def current_ability
    @current_ability ||= Ability.new(current_user, rails_session: session, panda_session: panda_session)
  end

  def current_pseudonym
    @current_pseudonym ||= current_user.pseudonyms.active.first || current_user.pseudonyms.first
  end

  def js_env(opts = {})
    if @js_env.nil?
      @js_env = {}
      @js_env[:params] = params.except('action', 'controller')
      @js_env[:csrf_token] = form_authenticity_token
      @js_env[:organization_id] = current_organization.id
      @js_env[:base_path] = url_for(path: nil, only_path: true)
      @js_env[:canvas_url] = current_organization.settings.dig(:canvas, :base_url)
      @js_env[:sentry_dsn] = Sentry.configuration&.dsn&.to_s
      @js_env[:environment] = Rails.env
      @js_env[:timezone] = account_timezone
      if panda_session.present?
        @js_env[:session_key] = current_session.session_key

        if current_session_data[:launch_params].present?
          @js_env[:post_message_token] = current_session_data[:launch_params][:custom_canvas_post_message_token]
          @js_env[:brand_variables] = parse_brand_config
          @js_env[:launch_params] = current_session_data[:launch_params]
        end

        @js_env[:permissions] = {
          super_user: current_ability.canvas_super_user?
        }
      end
    end
    @js_env.deep_merge! opts
    @js_env
  end
  helper_method :js_env

  def render_json(*, **kwargs)
    kwargs[:formats] = [:json]
    JSON.parse(render_to_string(*, **kwargs))
  end

  def account_timezone
    @account_timezone ||= Rails.cache.fetch('default_timezone', expires_in: 1.hour) do
      canvas_sync_client.account('self')['default_time_zone']
    rescue StandardError
      nil
    end
  end

  def user_timezone
    @user_timezone ||= Rails.cache.fetch([current_user, 'timezone'], expires_in: 1.hour) do
      canvas_sync_client.user_profile(current_user.canvas_id)['time_zone']
    rescue StandardError
      nil
    end
  end

  def panda_session
    current_session(create_missing: false)
  end

  def render_data(data, status = :ok)
    render json: { data: data }, status: status
  end

  def render_errors(errors)
    render json: { errors: errors }, status: :unprocessable_entity
  end

  private

  def parse_brand_config
    JSON.parse(current_session.custom_lti_params[:canvas_brand_config] || '{}')
  rescue StandardError
    {}
  end
end
