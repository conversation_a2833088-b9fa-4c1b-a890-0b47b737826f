# frozen_string_literal: true

class LtiController < ApplicationController
  before_action :set_js_env

  def account_navigation
    authorize! :launch_from, :account
    component = if current_ability.user_is_stride_admin? || current_ability.user_is_subaccount_admin?
                  'AdminDashboard'
                else
                  'UnAuthorized'
                end
    render component:, prerender: false
  end

  def course_navigation
    authorize! :launch_from, :course
    component = if current_ability.course_admin?
                  'CourseDashboard'
                else
                  'UnAuthorized'
                end

    render component:, prerender: false
  end

  private

  def set_js_env
    js_env({
             user_is_stride_admin: current_ability.user_is_stride_admin?,
             user_is_subaccount_admin: current_ability.user_is_subaccount_admin?,
             course_admin: current_ability.course_admin?,
             launch_point: action_name
           })
  end

  def handle_access_denied
    respond_to do |format|
      format.html { render component: 'UnAuthorized', prerender: false, status: :forbidden }
      format.json { render json: { error: 'Unauthorized' }, status: :unauthorized }
      format.any  { head :unauthorized }
    end
  end
end
